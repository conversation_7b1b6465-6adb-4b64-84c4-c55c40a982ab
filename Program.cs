﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace TaoBaoWorker
{
    internal class Program
    {
        static void Main(string[] args)
        {
            var host = Host.CreateDefaultBuilder(args)
                .ConfigureServices((context, services) =>
                {
                    // 添加HttpClientFactory
                    services.AddHttpClient();

                    // 注册其他服务
                   // services.AddTransient<MyService>();
                    services.AddHttpClient("taobao", client =>
                    {
                        const string appVer = "10.51.30";
                        client.DefaultRequestHeaders.Add("x-pv", "6.3");
                        client.DefaultRequestHeaders.Add("x-nettype", "WIFI");
                        client.DefaultRequestHeaders.Add("x-nq", "WIFI");
                        client.DefaultRequestHeaders.Add("x-region-channel", "CN");
                        client.DefaultRequestHeaders.Add("x-features", "27");
                        client.DefaultRequestHeaders.Add("x-app-edition", "ST");
                        client.DefaultRequestHeaders.Add("x-app-conf-v", "0");
                        client.DefaultRequestHeaders.Add("x-bx-version", "6.7.250602");
                        client.DefaultRequestHeaders.Add("f-refer", "mtop");
                        client.DefaultRequestHeaders.Add("x-extdata", "openappkey%3DDEFAULT_AUTH");
                        client.DefaultRequestHeaders.Add("x-ttid", $"600129%40taobao_android_{appVer}");
                        client.DefaultRequestHeaders.Add("x-app-ver", appVer);
                        client.DefaultRequestHeaders.Add("a-orange-dq", $"appKey=21646297&appVersion={appVer}&clientAppIndexVersion=1120250801213901061");
                        client.DefaultRequestHeaders.Add("x-appkey", "21646297");
                    });
                })
                .Build();
        }
    }
}
