﻿using Newtonsoft.Json;

namespace TaoBaoWorker.Services.TaoBao.Dtos
{
    public class TaobaoSignOutput
    {
        public string wua { get; set; }
        [JsonProperty("x-sgext")]
        public string xsgext { get; set; }
        [JsonProperty("x-umt")]
        public string xumt { get; set; }
        [JsonProperty("x-mini-wua")]
        public string xminiwua { get; set; }
        [JsonProperty("x-sign")]
        public string xsign { get; set; }
    }
}
