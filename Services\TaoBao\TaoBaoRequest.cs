﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.Http.Json;
using System.Text;
using TaoBaoWorker.Dtos;
using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker.Services.TaoBao
{
    public class TaoBaoRequest
    {
        private readonly ILogger<TaoBaoRequest> _logger;

        private readonly HttpClient _taoBaoHttpClient;
        private readonly HttpClient _httpClient;
        public const string AppVersion = "10.51.30";
        private const string TaoBaoSignServer = "http://192.168.50.23:8080/secret";
         
        public required TaoBaoUserDto User { get; set; }
        public TaoBaoRequest(ILogger<TaoBaoRequest> logger, IHttpClientFactory httpClient)
        {
            _logger = logger;
            _taoBaoHttpClient = httpClient.CreateClient("taobao");
            _httpClient = httpClient.CreateClient();
        }

        /// <summary>
        /// 发送GET请求
        /// </summary>
        /// <param name="mapPage"></param>
        /// <param name="domain"></param>
        /// <param name="api"></param>
        /// <param name="data"></param>
        /// <param name="ver"></param>
        /// <returns></returns>
        public async Task<BaseOutput> GetAsync(string domain, string api, string data, MapPageDto mapPage, string ver = "1.0")
        {


            TaoBaoSignInput sign = new TaoBaoSignInput
            {
                mapPage = mapPage,
                map = new MapDto
                {
                    Api = api,
                    DeviceId = User.DeviceId,
                    Sid = User.Sid,
                    Uid = User.Uid,
                    XFeatures = "27",
                    UtDid = User.Utdid,
                    TTid = $"600129@taobao_android_{AppVersion}",
                    T = GetTimeStamp(),
                    V = ver,
                    // mtopBusiness = "false",
                    Data = data
                }
            };

            var signResult = await GetSign(sign);

            if (signResult == null)
                return new BaseOutput { Code = 1000, Message = "获取签名失败" };


            var httpMessage = new HttpRequestMessage(HttpMethod.Get, $"http://{domain}/gw/{api}/{ver}/?data={Uri.EscapeDataString(data)}");
            var headers = new Dictionary<string, string>
            {
                { "x-sgext", UrlEncode(signResult.xsgext) },
                { "x-sign", UrlEncode(signResult.xsign) },
                { "x-sid", sign.map.Sid },
                { "x-uid", sign.map.Uid },
                { "x-mini-wua", UrlEncode(signResult.xminiwua) },
                { "x-t", sign.map.T },
                { "x-app-ver", AppVersion },
                { "x-umt", UrlEncode(signResult.xumt) },
                { "x-utdid", sign.map.UtDid },
                { "x-appkey", sign.appKey },
                { "x-page-url", UrlEncode(sign.mapPage.PageId) },
                { "x-page-name", sign.mapPage.PageName },
                { "x-devid", sign.map.DeviceId },
            };
            foreach (var header in headers)
            {
                httpMessage.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            var resp = await _taoBaoHttpClient.SendAsync(httpMessage);
            var content = await resp.Content.ReadAsStringAsync();
            return FormatResponse(content);
        }

        /// <summary>
        /// 发送POST请求
        /// </summary>
        /// <param name="page"></param>
        /// <param name="domain"></param>
        /// <param name="api"></param>
        /// <param name="data"></param>
        /// <param name="ver"></param>
        /// <param name="needWua"></param>
        /// <param name="otherHeaders"></param>
        /// <returns></returns>
        public async Task<BaseOutput> PostAsync(MapPageDto page, string domain, string api, string data,
            string ver = "4.0", bool needWua = false, Dictionary<string, string>? otherHeaders = null)
        {
            TaoBaoSignInput sign = new TaoBaoSignInput
            {
                mapPage = page,
                map =
                {
                    Api = api,
                    DeviceId = User.DeviceId,
                    Sid = User.Sid,
                    Uid = User.Uid,
                    XFeatures = "27",
                    UtDid =  User.Utdid,
                    TTid = $"600129@taobao_android_{AppVersion}",
                    T = GetTimeStamp(),
                    V = ver,
                    Data = data
                },
                needWua = needWua
            };
            var signResult = await GetSign(sign);
            if (signResult == null)
                return new BaseOutput { Code = 1000, Message = "获取签名失败" };

            object model;
            if (string.IsNullOrEmpty(signResult.wua))
            {
                model = new { data };
            }
            else
            {
                model = new { data, signResult.wua };
            }

            var headers = new Dictionary<string, string>
            {
                {"x-sgext", UrlEncode(signResult.xsgext)},
                {"x-sign", UrlEncode(signResult.xsign)},
                {"x-sid", sign.map.Sid},
                {"x-uid", sign.map.Uid},
                {"x-features", sign.map.XFeatures},
                {"x-mini-wua", UrlEncode(signResult.xminiwua)},
                {"x-t", sign.map.T},
                {"x-ttid", UrlEncode(sign.map.TTid)},
                {"x-app-ver", AppVersion},
                {"x-umt", UrlEncode(signResult.xumt)},
                {"x-utdid", sign.map.UtDid},
                {"x-appkey", sign.appKey},
                {"x-page-url", UrlEncode(sign.mapPage.PageId)},
                {"x-page-name", sign.mapPage.PageName},
                {"x-devid", sign.map.DeviceId},
            };
            if (otherHeaders != null)
            {
                foreach (var otherHeader in otherHeaders)
                {
                    if (!headers.ContainsKey(otherHeader.Key))
                        headers.Add(otherHeader.Key, otherHeader.Value);
                }
            }
            var httpMessage = new HttpRequestMessage(HttpMethod.Post, $"{domain}/gw/{api}/{ver}/");
            var content = JsonConvert.SerializeObject(model);
            httpMessage.Content = new StringContent(content, Encoding.UTF8, "application/json");
            foreach (var header in headers)
            {
                httpMessage.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }


            var resp = await _taoBaoHttpClient.SendAsync(httpMessage);
            var response = await resp.Content.ReadAsStringAsync();
            return FormatResponse(response);
        }

        private BaseOutput FormatResponse(string response)
        {
            var json = JObject.Parse(response);
            var ret = json["ret"];
            if (ret is { Type: JTokenType.Array })
            {
                var retArr = ret as JArray;
                if (retArr?.Count > 0)
                {
                    var retVal = retArr[0].ToString();
                    if (retVal == "SUCCESS::调用成功")
                    {
                        if (json.TryGetValue("data", out var data))
                            return new BaseOutput { Code = 0, Data = (JObject)data };
                    }
                    else
                        return new BaseOutput { Code = 1, Message =retVal };
                }

            }
            if (json.TryGetValue("data", out var unexpectedData))
                return new BaseOutput { Code = 0, Data = (JObject)unexpectedData };
            _logger.LogError($"处理响应时出现未知的返回值：{response}");
            return new BaseOutput { Code = 1, Message = "未知的返回值" };
        }

        /// <summary>
        /// 参数签名
        /// </summary>
        /// <param name="postData"></param>
        /// <returns></returns>
        private async Task<TaobaoSignOutput?> GetSign(TaoBaoSignInput postData)
        {
            try
            {
                var resp = await _httpClient.PostAsJsonAsync(TaoBaoSignServer, postData);
                if (resp.IsSuccessStatusCode)
                {
                    var content = await resp.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<TaobaoSignOutput>(content);
                }

            }
            catch (Exception e)
            {
                _logger.LogError(e, "获取参数签名发生异常");
            }

            return null;
        }

        private static string UrlEncode(string str)
        {
            return UrlEncode(str, Encoding.UTF8);
        }
        private static string UrlEncode(string str, Encoding e)
        {

            return Encoding.ASCII.GetString(UrlEncodeToBytes(str, e));
        }
        private static byte[] UrlEncodeToBytes(string str, Encoding e)
        {

            byte[] bytes = e.GetBytes(str);
            return UrlEncodeBytesToBytesInternal(bytes, 0, bytes.Length, false);
        }
        private static byte[] UrlEncodeBytesToBytesInternal(byte[] bytes, int offset, int count, bool alwaysCreateReturnValue)
        {
            int num = 0;
            int num2 = 0;
            for (int i = 0; i < count; i++)
            {
                char ch = (char)bytes[offset + i];
                if (ch == ' ')
                {
                    num++;
                }
                else if (!IsSafe(ch))
                {
                    num2++;
                }
            }
            if (!alwaysCreateReturnValue && num == 0 && num2 == 0)
            {
                return bytes;
            }
            byte[] buffer = new byte[count + num2 * 2];
            int num4 = 0;
            for (int j = 0; j < count; j++)
            {
                byte num6 = bytes[offset + j];
                char ch2 = (char)num6;
                if (IsSafe(ch2))
                {
                    buffer[num4++] = num6;
                }
                else if (ch2 == ' ')
                {
                    buffer[num4++] = 43;
                }
                else
                {
                    buffer[num4++] = 37;
                    buffer[num4++] = (byte)IntToHex(num6 >> 4 & 15);
                    buffer[num4++] = (byte)IntToHex(num6 & 15);
                }
            }
            return buffer;
        }
        private static bool IsSafe(char ch)
        {
            if (ch is >= 'a' and <= 'z' || ch is >= 'A' and <= 'Z' || ch is >= '0' and <= '9')
            {
                return true;
            }
            switch (ch)
            {
                case '\'':
                case '(':
                case ')':
                case '*':
                case '-':
                case '.':
                case '_':
                case '!':
                    return true;
            }
            return false;
        }
        private static char IntToHex(int n)
        {
            if (n <= 9)
            {
                return (char)(n + 48);
            }
            return (char)(n - 10 + 65);
        }

        /// <summary>
        /// 获取时间戳
        /// </summary>
        /// <returns></returns>
        private static string GetTimeStamp()
        {
            var epoch = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
            return epoch.ToString();
        }


    }
}