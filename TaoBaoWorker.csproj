﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\TaoBao\新文件夹\**" />
    <Compile Remove="新文件夹\**" />
    <EmbeddedResource Remove="Services\TaoBao\新文件夹\**" />
    <EmbeddedResource Remove="新文件夹\**" />
    <None Remove="Services\TaoBao\新文件夹\**" />
    <None Remove="新文件夹\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog" Version="6.0.2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Common\" />
  </ItemGroup>

</Project>
