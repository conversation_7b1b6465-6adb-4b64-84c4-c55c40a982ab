using TaoBaoWorker.Services.TaoBao.Dtos;

namespace TaoBaoWorker.Services.TaoBao
{
    internal class TaoBaoService
    {
        private readonly TaoBaoRequest _request;

        public TaoBaoService(TaoBaoRequest request)
        {
            _request = request;
            _request.
        }

        public async Task OrderDetail(string orderId)
        {
            var data = "{\"appName\":\"tborder\",\"appVersion\":\"3.0\",\"archive\":\"false\",\"bizOrderId\":\""+orderId+"\",\"extParams\":\"{\\\"weexPage\\\":\\\"neworderdetail4\\\",\\\"openFrom\\\":\\\"taoBoughtList\\\",\\\"installApp\\\":\\\"ALIPAY\\\"}\",\"from\":\"OrderListActivity\",\"useV2\":\"true\"}";
            var resp = await _request.GetAsync("trade-acs.m.taobao.com", "mtop.taobao.order.query.detailv2", data, new MapPageDto("http://h5.m.taobao.com/awp/base/order/listultron.htm", "com.taobao.tao.welcome.Welcome"));
            if (resp.Success)
            {

            }
        }
    }
}
