﻿using Newtonsoft.Json;

namespace TaoBaoWorker.Services.TaoBao.Dtos
{
    public class MapDto
    {
        [JsonProperty("data", Order = 1)]
        public required string Data { get; set; }
        [JsonProperty("deviceId", Order = 2)]
        public required string DeviceId { get; set; }
        [JsonProperty("sid", Order = 3)]
        public required string Sid { get; set; }
        [Json<PERSON>roperty("uid", Order = 4)]
        public required string Uid { get; set; }

        [JsonProperty("x-features", Order = 5)]
        public required string XFeatures { get; set; }

        [JsonProperty("appKey", Order = 6)]
        public string AppKey => "21646297";

        [JsonProperty("api", Order = 7)]
        public required string Api { get; set; }

        [Json<PERSON>roperty("mtopBusiness", Order = 8)]
        public  string? MTopBusiness { get; set; } = "true";

        [JsonProperty("utdid", Order = 9)]
        public required string UtDid { get; set; }

        [JsonProperty("extdata", Order = 10)]
        public string ExtData => "openappkey=DEFAULT_AUTH";

        [<PERSON><PERSON><PERSON>roperty("ttid", Order = 11)]
        public required string TTid { get; set; }

        [JsonProperty("t", Order = 12)]
        public required string T { get; set; }

        [JsonProperty("t", Order = 13)]
        public required string V { get; set; }
    }
}
